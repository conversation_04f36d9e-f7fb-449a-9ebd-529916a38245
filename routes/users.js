var express = require('express');
var router = express.Router();
const { User } = require('../db/models');

// 路由级中间件 - 只对 router 生效
router.use((req, res, next) => {
  console.log('📌 用户路由中间件触发');
  next();
});

// 参数验证中间件
const validateUserData = (req, res, next) => {
  const { firstName, lastName, email } = req.body;

  if (!firstName || !lastName || !email) {
    return res.status(400).json({
      success: false,
      message: '缺少必要字段：firstName, lastName, email'
    });
  }

  // 简单的邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({
      success: false,
      message: '邮箱格式不正确'
    });
  }

  next();
};

// 统一错误处理函数
const handleError = (res, error, defaultMessage = '服务器内部错误') => {
  console.error('Error:', error);

  // 如果是 Sequelize 验证错误
  if (error.name === 'SequelizeValidationError') {
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors: error.errors.map(err => ({
        field: err.path,
        message: err.message
      }))
    });
  }

  // 如果是唯一约束错误
  if (error.name === 'SequelizeUniqueConstraintError') {
    return res.status(409).json({
      success: false,
      message: '数据已存在',
      errors: error.errors.map(err => ({
        field: err.path,
        message: err.message
      }))
    });
  }

  res.status(500).json({
    success: false,
    message: error.message || defaultMessage
  });
};

/**
 * @route GET /users
 * @desc 获取用户列表（支持分页和搜索）
 * @query {number} page - 页码（默认1）
 * @query {number} limit - 每页数量（默认10）
 * @query {string} search - 搜索关键词
 */
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search } = req.query;

    let result;
    if (search) {
      // 如果有搜索关键词，使用搜索功能
      const users = await User.searchUsers(search, { page, limit });
      result = {
        users,
        total: users.length,
        totalPages: Math.ceil(users.length / limit),
        currentPage: parseInt(page)
      };
    } else {
      // 否则获取所有用户
      result = await User.getAllUsers({ page, limit });
    }

    res.json({
      success: true,
      message: '获取用户列表成功',
      data: result
    });
  } catch (error) {
    handleError(res, error, '获取用户列表失败');
  }
});

/**
 * @route GET /users/:id
 * @desc 根据ID获取单个用户
 * @param {number} id - 用户ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    const user = await User.getUserById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: user
    });
  } catch (error) {
    handleError(res, error, '获取用户信息失败');
  }
});

/**
 * @route POST /users
 * @desc 创建新用户
 * @body {string} firstName - 名字
 * @body {string} lastName - 姓氏
 * @body {string} email - 邮箱
 */
router.post('/', validateUserData, async (req, res) => {
  try {
    const { firstName, lastName, email } = req.body;

    // 检查邮箱是否已存在
    const existingUser = await User.getUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: '邮箱已存在'
      });
    }

    const user = await User.createUser({
      firstName,
      lastName,
      email
    });

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: user
    });
  } catch (error) {
    handleError(res, error, '创建用户失败');
  }
});

/**
 * @route PUT /users/:id
 * @desc 更新用户信息
 * @param {number} id - 用户ID
 * @body {string} firstName - 名字
 * @body {string} lastName - 姓氏
 * @body {string} email - 邮箱
 */
router.put('/:id', validateUserData, async (req, res) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, email } = req.body;

    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 检查邮箱是否被其他用户使用
    const existingUser = await User.getUserByEmail(email);
    if (existingUser && existingUser.id !== parseInt(id)) {
      return res.status(409).json({
        success: false,
        message: '邮箱已被其他用户使用'
      });
    }

    const updatedUser = await User.updateUser(id, {
      firstName,
      lastName,
      email
    });

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    });
  } catch (error) {
    handleError(res, error, '更新用户信息失败');
  }
});

/**
 * @route DELETE /users/:id
 * @desc 删除单个用户
 * @param {number} id - 用户ID
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    const deleted = await User.deleteUser(id);

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    handleError(res, error, '删除用户失败');
  }
});

/**
 * @route DELETE /users
 * @desc 批量删除用户
 * @body {number[]} ids - 用户ID数组
 */
router.delete('/', async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的用户ID数组'
      });
    }

    // 验证所有ID都是数字
    const invalidIds = ids.filter(id => isNaN(id));
    if (invalidIds.length > 0) {
      return res.status(400).json({
        success: false,
        message: '包含无效的用户ID'
      });
    }

    const deletedCount = await User.deleteUsers(ids);

    res.json({
      success: true,
      message: `成功删除 ${deletedCount} 个用户`,
      data: { deletedCount }
    });
  } catch (error) {
    handleError(res, error, '批量删除用户失败');
  }
});

/**
 * @route PATCH /users/:id
 * @desc 部分更新用户信息
 * @param {number} id - 用户ID
 * @body {string} [firstName] - 名字（可选）
 * @body {string} [lastName] - 姓氏（可选）
 * @body {string} [email] - 邮箱（可选）
 */
router.patch('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    if (!id || isNaN(id)) {
      return res.status(400).json({
        success: false,
        message: '无效的用户ID'
      });
    }

    // 检查是否有要更新的数据
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要更新的数据'
      });
    }

    // 如果更新邮箱，检查邮箱格式和唯一性
    if (updateData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updateData.email)) {
        return res.status(400).json({
          success: false,
          message: '邮箱格式不正确'
        });
      }

      const existingUser = await User.getUserByEmail(updateData.email);
      if (existingUser && existingUser.id !== parseInt(id)) {
        return res.status(409).json({
          success: false,
          message: '邮箱已被其他用户使用'
        });
      }
    }

    const updatedUser = await User.updateUser(id, updateData);

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    });
  } catch (error) {
    handleError(res, error, '更新用户信息失败');
  }
});

/**
 * @route GET /users/email/:email
 * @desc 根据邮箱获取用户
 * @param {string} email - 用户邮箱
 */
router.get('/email/:email', async (req, res) => {
  try {
    const { email } = req.params;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱地址'
      });
    }

    const user = await User.getUserByEmail(email);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: user
    });
  } catch (error) {
    handleError(res, error, '获取用户信息失败');
  }
});

module.exports = router;
