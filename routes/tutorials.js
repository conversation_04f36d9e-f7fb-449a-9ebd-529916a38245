const express = require('express');
const router = express.Router();
const db = require('../db/models');
const Tutorial = db.Tutorial;

// 处理 /api/tutorials 路由

// 获取所有教程，支持按标题模糊查询
router.get('/', async (req, res, next) => {
  const { title } = req.query;
  let tutorials;
  if (title) {
    tutorials = await Tutorial.findByTitleKeyword(title);
  } else {
    tutorials = await Tutorial.getAll();
  }
  res.json(tutorials);
});

// 获取已发布的教程
router.get('/published', async (req, res, next) => {
  const tutorials = await Tutorial.findAllPublished();
  res.json(tutorials);
});

// 根据ID获取教程
router.get('/:id', async (req, res, next) => {
  const tutorial = await Tutorial.getById(req.params.id);
  if (!tutorial) return res.status(404).json({ message: 'Not found' });
  res.json(tutorial);
});

// 新增教程
router.post('/', async (req, res, next) => {
  try {
    const tutorial = await Tutorial.createTutorial(req.body);
    res.status(201).json(tutorial);
  }catch(e) {
    next(new Error('无法新建'));
  }

});

// 根据ID更新教程
router.put('/:id', async (req, res, next) => {
  const tutorial = await Tutorial.updateById(req.params.id, req.body);
  if (!tutorial) return res.status(404).json({ message: 'Not found' });
  res.json(tutorial);
});

// 根据ID删除教程
router.delete('/:id', async (req, res, next) => {
  const deleted = await Tutorial.removeById(req.params.id);
  if (!deleted) return res.status(404).json({ message: 'Not found' });
  res.json({ message: 'Deleted successfully' });
});

// 删除所有教程
router.delete('/', async (req, res, next) => {
  await Tutorial.removeAll();
  res.json({ message: 'All tutorials deleted' });
});

module.exports = router;
