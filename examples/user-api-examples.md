# 用户 API 使用示例

本文档展示了如何使用用户管理 API 的各种功能。

## API 端点概览

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/users` | 获取用户列表（支持分页和搜索） |
| GET | `/users/:id` | 根据ID获取单个用户 |
| GET | `/users/email/:email` | 根据邮箱获取用户 |
| POST | `/users` | 创建新用户 |
| PUT | `/users/:id` | 完整更新用户信息 |
| PATCH | `/users/:id` | 部分更新用户信息 |
| DELETE | `/users/:id` | 删除单个用户 |
| DELETE | `/users` | 批量删除用户 |

## 使用示例

### 1. 创建用户

```bash
curl -X POST http://localhost:3000/users \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "张",
    "lastName": "三",
    "email": "<EMAIL>"
  }'
```

**响应示例：**
```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "id": 1,
    "firstName": "张",
    "lastName": "三",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. 获取用户列表

```bash
# 获取所有用户（默认分页）
curl http://localhost:3000/users

# 分页获取用户
curl "http://localhost:3000/users?page=1&limit=5"

# 搜索用户
curl "http://localhost:3000/users?search=张"
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取用户列表成功",
  "data": {
    "users": [
      {
        "id": 1,
        "firstName": "张",
        "lastName": "三",
        "email": "<EMAIL>",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

### 3. 根据ID获取用户

```bash
curl http://localhost:3000/users/1
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "firstName": "张",
    "lastName": "三",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 4. 根据邮箱获取用户

```bash
curl http://localhost:3000/users/email/<EMAIL>
```

### 5. 完整更新用户信息

```bash
curl -X PUT http://localhost:3000/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "李",
    "lastName": "四",
    "email": "<EMAIL>"
  }'
```

### 6. 部分更新用户信息

```bash
# 只更新邮箱
curl -X PATCH http://localhost:3000/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'

# 只更新名字
curl -X PATCH http://localhost:3000/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "王"
  }'
```

### 7. 删除单个用户

```bash
curl -X DELETE http://localhost:3000/users/1
```

**响应示例：**
```json
{
  "success": true,
  "message": "用户删除成功"
}
```

### 8. 批量删除用户

```bash
curl -X DELETE http://localhost:3000/users \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3]
  }'
```

**响应示例：**
```json
{
  "success": true,
  "message": "成功删除 3 个用户",
  "data": {
    "deletedCount": 3
  }
}
```

## 错误处理

API 使用统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ]
}
```

### 常见错误状态码

- `400 Bad Request`: 请求参数错误或验证失败
- `404 Not Found`: 用户不存在
- `409 Conflict`: 数据冲突（如邮箱已存在）
- `500 Internal Server Error`: 服务器内部错误

## 数据验证规则

- `firstName`: 必填，长度1-50个字符
- `lastName`: 必填，长度1-50个字符
- `email`: 必填，必须是有效的邮箱格式，且唯一

## 功能特性

1. **数据验证**: 自动验证输入数据的格式和完整性
2. **唯一性检查**: 确保邮箱地址的唯一性
3. **分页支持**: 支持分页获取用户列表
4. **搜索功能**: 支持按姓名和邮箱搜索用户
5. **软删除**: 启用了软删除功能，删除的数据可以恢复
6. **时间戳**: 自动记录创建和更新时间
7. **错误处理**: 统一的错误处理和响应格式
8. **批量操作**: 支持批量删除用户

## 测试建议

建议使用 Postman、Insomnia 或类似工具来测试这些 API 端点。你也可以编写单元测试来确保功能的正确性。
