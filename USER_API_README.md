# 用户管理 API 实现

本项目为 Express.js 应用添加了完整的用户管理功能，包括 RESTful API 路由和数据库模型的增删改查操作。

## 📁 文件结构

```
├── db/models/user.js          # 用户模型（包含增删改查方法）
├── routes/users.js            # 用户路由（RESTful API）
├── test/
│   ├── user-model.test.js     # 模型测试
│   └── user-routes.test.js    # 路由测试
├── examples/
│   └── user-api-examples.md   # API 使用示例
└── USER_API_README.md         # 本文档
```

## 🚀 功能特性

### 数据库模型功能 (`db/models/user.js`)

- ✅ **创建用户** - `createUser(userData)`
- ✅ **根据ID获取用户** - `getUserById(id)`
- ✅ **根据邮箱获取用户** - `getUserByEmail(email)`
- ✅ **获取用户列表** - `getAllUsers(options)` (支持分页)
- ✅ **更新用户** - `updateUser(id, updateData)`
- ✅ **删除用户** - `deleteUser(id)`
- ✅ **批量删除用户** - `deleteUsers(ids)`
- ✅ **搜索用户** - `searchUsers(keyword, options)`

### API 路由功能 (`routes/users.js`)

| 方法 | 端点 | 功能 |
|------|------|------|
| `GET` | `/users` | 获取用户列表（支持分页和搜索） |
| `GET` | `/users/:id` | 根据ID获取单个用户 |
| `GET` | `/users/email/:email` | 根据邮箱获取用户 |
| `POST` | `/users` | 创建新用户 |
| `PUT` | `/users/:id` | 完整更新用户信息 |
| `PATCH` | `/users/:id` | 部分更新用户信息 |
| `DELETE` | `/users/:id` | 删除单个用户 |
| `DELETE` | `/users` | 批量删除用户 |

## 🔧 技术特性

### 数据验证
- 字段必填验证（firstName, lastName, email）
- 邮箱格式验证
- 字符串长度验证（1-50字符）
- 邮箱唯一性验证

### 数据库特性
- **时间戳**: 自动记录 `createdAt` 和 `updatedAt`
- **软删除**: 启用 `paranoid` 模式，删除的数据可恢复
- **索引优化**: 邮箱字段唯一索引

### API 特性
- **统一响应格式**: 所有 API 返回一致的 JSON 格式
- **错误处理**: 完善的错误处理和状态码
- **参数验证**: 请求参数和数据验证
- **分页支持**: 用户列表支持分页
- **搜索功能**: 支持按姓名和邮箱搜索

## 📝 使用示例

### 创建用户
```javascript
// 使用模型方法
const user = await User.createUser({
  firstName: '张',
  lastName: '三',
  email: '<EMAIL>'
});

// 使用 API
curl -X POST http://localhost:3000/users \
  -H "Content-Type: application/json" \
  -d '{"firstName":"张","lastName":"三","email":"<EMAIL>"}'
```

### 获取用户列表
```javascript
// 使用模型方法
const result = await User.getAllUsers({ page: 1, limit: 10 });

// 使用 API
curl "http://localhost:3000/users?page=1&limit=10"
```

### 搜索用户
```javascript
// 使用模型方法
const users = await User.searchUsers('张');

// 使用 API
curl "http://localhost:3000/users?search=张"
```

## 🧪 测试

项目包含完整的测试套件：

### 运行测试
```bash
# 安装测试依赖
npm install --save-dev jest supertest

# 运行模型测试
npm test test/user-model.test.js

# 运行路由测试
npm test test/user-routes.test.js

# 运行所有测试
npm test
```

### 测试覆盖
- ✅ 模型方法测试（创建、读取、更新、删除）
- ✅ API 路由测试（所有端点）
- ✅ 数据验证测试
- ✅ 错误处理测试
- ✅ 边界条件测试

## 📋 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 返回的数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ]
}
```

## 🔍 状态码说明

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `409 Conflict`: 数据冲突（如邮箱重复）
- `500 Internal Server Error`: 服务器内部错误

## 🚦 下一步建议

1. **添加认证授权**: 实现 JWT 或 Session 认证
2. **添加更多字段**: 如头像、电话号码、地址等
3. **实现关联关系**: 与其他模型建立关联
4. **添加缓存**: 使用 Redis 缓存热点数据
5. **API 文档**: 使用 Swagger 生成 API 文档
6. **日志记录**: 添加操作日志和审计功能

## 📚 相关文档

- [API 使用示例](examples/user-api-examples.md)
- [模型测试](test/user-model.test.js)
- [路由测试](test/user-routes.test.js)

---

**注意**: 在生产环境中使用前，请确保：
1. 配置正确的数据库连接
2. 添加适当的安全措施
3. 实现用户认证和授权
4. 添加请求限制和防护措施
