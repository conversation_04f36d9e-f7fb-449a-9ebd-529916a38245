// .sequelizerc
const path = require('path');

module.exports = {
  config: path.resolve('db', 'config.json'),
  'models-path': path.resolve('db', 'models'),
  'seeders-path': path.resolve('db', 'seeders'),
  'migrations-path': path.resolve('db', 'migrations'),
};

// module.exports = {
//   config: path.resolve('db', 'config.js'), // 建议使用js 文件，因为生产环境建议使用环境变量
//   'models-path': path.resolve('db', 'models'),
//   'seeders-path': path.resolve('db', 'seeders'),
//   'migrations-path': path.resolve('db', 'migrations'),
// };



