'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    // 通过 bulkInsert 方法批量插入数据
    // 🍊注意 这俩是数据库的表名，不是模型名称
    return queryInterface.bulkInsert('Tutorials', [
      {
        title: '这是标题一',
        description: '这是标题描述111111',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        title: '这是标题二',
        description: '这是标题描述2222',
        published: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        title: '这是标题三',
        published: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        title: '这是标题四',
        description: '这是标题描述4444',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    return queryInterface.bulkDelete('Tutorials', null, {});
  }
};
