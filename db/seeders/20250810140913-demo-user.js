'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert('Users', [{
      firstName: '张',
      lastName: '三',
      email: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      firstName: '李',
      lastName: '四',
      email: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date()
    }, {
      firstName: '王',
      lastName: '五',
      email: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date()
    }, {
      firstName: '赵',
      lastName: '六',
      email: '<EMAIL>',
      createdAt: new Date(),
      updatedAt: new Date()
    }]);
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('Users', null, {});
  }
};
