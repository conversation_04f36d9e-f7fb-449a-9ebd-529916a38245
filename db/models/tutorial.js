'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Tutorial extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }

    // 获取所有教程
    static async getAll() {
      return await this.findAll();
    }

    // 根据ID获取教程
    static async getById(id) {
      return await this.findByPk(id);
    }

    // 新增教程
    static async createTutorial(data) {
      return await this.create(data);
    }

    // 根据ID更新教程
    static async updateById(id, data) {
      const tutorial = await this.findByPk(id);
      if (!tutorial) return null;
      return await tutorial.update(data);
    }

    // 根据ID删除教程
    static async removeById(id) {
      return await this.destroy({ where: { id } });
    }

    // 删除所有教程
    static async removeAll() {
      return await this.destroy({ where: {}, truncate: true });
    }

    // 查找所有已发布的教程
    static async findAllPublished() {
      return await this.findAll({ where: { published: true } });
    }

    // 按标题模糊查找
    static async findByTitleKeyword(kw) {
      const condition = {
        where: {
          title: {
            [sequelize.Sequelize.Op.like]: `%${kw}%`
          }
        }
      };
      return await this.findAll(condition);
    }
  }
  Tutorial.init({
    title: DataTypes.STRING,
    description: {
        type: DataTypes.STRING,
        allowNull: true
    },
    published: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    sequelize,
    modelName: 'Tutorial',
  });
  return Tutorial;
};