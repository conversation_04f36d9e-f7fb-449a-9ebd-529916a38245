'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Comment extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      Comment.belongsTo(models.Tutorial, {
        foreignKey: 'tutorialId',
        as: "tutorial"
      });
    }
  }
  Comment.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '名字不能为空'
        },
        len: {
          args: [1, 50],
          msg: '名字长度必须在1-50个字符之间'
        }
      }
    },
    text: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '评论不能为空'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Comment',
  });


  return Comment;
};