'use strict';
const {
  Model
} = require('sequelize');
const Tutorial = require('./tutorial');
module.exports = (sequelize, DataTypes) => {
  class Comment extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  Comment.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '名字不能为空'
        },
        len: {
          args: [1, 50],
          msg: '名字长度必须在1-50个字符之间'
        }
      }
    },
    text: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '评论不能为空'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Comment',
  });

  Tutorial.hasMany(Comment, { as: 'comments' }); // 给这个关联起一个别名 叫 comments
  Comment.belongsTo(Tutorial, { foreignKey: 'tutorialId', as: "tutorial" }); // 指定 Tutorial表中外键字段的名字叫 tutorialId，并给这个关联起一个别名叫 tutorial
  return Comment;
};