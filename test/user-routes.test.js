const request = require('supertest');
const express = require('express');
const userRoutes = require('../routes/users');
const { User } = require('../db/models');
const { sequelize } = require('../db/models');

// 创建测试应用
const app = express();
app.use(express.json());
app.use('/users', userRoutes);

describe('User Routes', () => {
  // 在每个测试前清理数据库
  beforeEach(async () => {
    await User.destroy({ where: {}, force: true });
  });

  // 在所有测试完成后关闭数据库连接
  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /users', () => {
    test('应该成功创建用户', async () => {
      const userData = {
        firstName: '张',
        lastName: '三',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/users')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('用户创建成功');
      expect(response.body.data.firstName).toBe('张');
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    test('应该在缺少必填字段时返回400错误', async () => {
      const userData = {
        firstName: '张',
        // 缺少 lastName 和 email
      };

      const response = await request(app)
        .post('/users')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('缺少必要字段：firstName, lastName, email');
    });

    test('应该在邮箱格式错误时返回400错误', async () => {
      const userData = {
        firstName: '张',
        lastName: '三',
        email: 'invalid-email'
      };

      const response = await request(app)
        .post('/users')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('邮箱格式不正确');
    });

    test('应该在邮箱已存在时返回409错误', async () => {
      const userData = {
        firstName: '张',
        lastName: '三',
        email: '<EMAIL>'
      };

      // 先创建一个用户
      await request(app)
        .post('/users')
        .send(userData)
        .expect(201);

      // 尝试创建相同邮箱的用户
      const response = await request(app)
        .post('/users')
        .send(userData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('邮箱已存在');
    });
  });

  describe('GET /users', () => {
    test('应该获取用户列表', async () => {
      // 创建测试用户
      const users = [
        { firstName: '用户', lastName: '1', email: '<EMAIL>' },
        { firstName: '用户', lastName: '2', email: '<EMAIL>' }
      ];

      for (const userData of users) {
        await User.createUser(userData);
      }

      const response = await request(app)
        .get('/users')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.users).toHaveLength(2);
      expect(response.body.data.total).toBe(2);
    });

    test('应该支持分页', async () => {
      // 创建多个用户
      for (let i = 1; i <= 5; i++) {
        await User.createUser({
          firstName: '用户',
          lastName: i.toString(),
          email: `user${i}@example.com`
        });
      }

      const response = await request(app)
        .get('/users?page=1&limit=2')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.users).toHaveLength(2);
      expect(response.body.data.total).toBe(5);
      expect(response.body.data.totalPages).toBe(3);
      expect(response.body.data.currentPage).toBe(1);
    });

    test('应该支持搜索', async () => {
      const users = [
        { firstName: '张', lastName: '三', email: '<EMAIL>' },
        { firstName: '李', lastName: '四', email: '<EMAIL>' },
        { firstName: '张', lastName: '五', email: '<EMAIL>' }
      ];

      for (const userData of users) {
        await User.createUser(userData);
      }

      const response = await request(app)
        .get('/users?search=张')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.users).toHaveLength(2);
    });
  });

  describe('GET /users/:id', () => {
    test('应该根据ID获取用户', async () => {
      const user = await User.createUser({
        firstName: '测试',
        lastName: '用户',
        email: '<EMAIL>'
      });

      const response = await request(app)
        .get(`/users/${user.id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(user.id);
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    test('应该在用户不存在时返回404错误', async () => {
      const response = await request(app)
        .get('/users/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('用户不存在');
    });

    test('应该在ID无效时返回400错误', async () => {
      const response = await request(app)
        .get('/users/invalid')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('无效的用户ID');
    });
  });

  describe('PUT /users/:id', () => {
    test('应该成功更新用户信息', async () => {
      const user = await User.createUser({
        firstName: '原',
        lastName: '名',
        email: '<EMAIL>'
      });

      const updateData = {
        firstName: '新',
        lastName: '名',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .put(`/users/${user.id}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.firstName).toBe('新');
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    test('应该在用户不存在时返回404错误', async () => {
      const updateData = {
        firstName: '新',
        lastName: '名',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .put('/users/999')
        .send(updateData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('用户不存在');
    });
  });

  describe('DELETE /users/:id', () => {
    test('应该成功删除用户', async () => {
      const user = await User.createUser({
        firstName: '待删除',
        lastName: '用户',
        email: '<EMAIL>'
      });

      const response = await request(app)
        .delete(`/users/${user.id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('用户删除成功');
    });

    test('应该在用户不存在时返回404错误', async () => {
      const response = await request(app)
        .delete('/users/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('用户不存在');
    });
  });
});
