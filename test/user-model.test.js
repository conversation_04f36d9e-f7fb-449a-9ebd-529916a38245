const { User } = require('../db/models');
const { sequelize } = require('../db/models');

describe('User Model', () => {
  // 在每个测试前清理数据库
  beforeEach(async () => {
    await User.destroy({ where: {}, force: true });
  });

  // 在所有测试完成后关闭数据库连接
  afterAll(async () => {
    await sequelize.close();
  });

  describe('createUser', () => {
    test('应该成功创建用户', async () => {
      const userData = {
        firstName: '张',
        lastName: '三',
        email: '<EMAIL>'
      };

      const user = await User.createUser(userData);

      expect(user).toBeDefined();
      expect(user.firstName).toBe('张');
      expect(user.lastName).toBe('三');
      expect(user.email).toBe('<EMAIL>');
      expect(user.id).toBeDefined();
    });

    test('应该在缺少必填字段时抛出错误', async () => {
      const userData = {
        firstName: '张',
        // 缺少 lastName 和 email
      };

      await expect(User.createUser(userData)).rejects.toThrow();
    });

    test('应该在邮箱格式错误时抛出错误', async () => {
      const userData = {
        firstName: '张',
        lastName: '三',
        email: 'invalid-email'
      };

      await expect(User.createUser(userData)).rejects.toThrow();
    });
  });

  describe('getUserById', () => {
    test('应该根据ID获取用户', async () => {
      const userData = {
        firstName: '李',
        lastName: '四',
        email: '<EMAIL>'
      };

      const createdUser = await User.createUser(userData);
      const foundUser = await User.getUserById(createdUser.id);

      expect(foundUser).toBeDefined();
      expect(foundUser.id).toBe(createdUser.id);
      expect(foundUser.email).toBe('<EMAIL>');
    });

    test('应该在用户不存在时返回null', async () => {
      const user = await User.getUserById(999);
      expect(user).toBeNull();
    });
  });

  describe('getUserByEmail', () => {
    test('应该根据邮箱获取用户', async () => {
      const userData = {
        firstName: '王',
        lastName: '五',
        email: '<EMAIL>'
      };

      await User.createUser(userData);
      const foundUser = await User.getUserByEmail('<EMAIL>');

      expect(foundUser).toBeDefined();
      expect(foundUser.email).toBe('<EMAIL>');
    });

    test('应该在用户不存在时返回null', async () => {
      const user = await User.getUserByEmail('<EMAIL>');
      expect(user).toBeNull();
    });
  });

  describe('getAllUsers', () => {
    test('应该获取所有用户并支持分页', async () => {
      // 创建多个用户
      const users = [
        { firstName: '用户', lastName: '1', email: '<EMAIL>' },
        { firstName: '用户', lastName: '2', email: '<EMAIL>' },
        { firstName: '用户', lastName: '3', email: '<EMAIL>' }
      ];

      for (const userData of users) {
        await User.createUser(userData);
      }

      const result = await User.getAllUsers({ page: 1, limit: 2 });

      expect(result.users).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.totalPages).toBe(2);
      expect(result.currentPage).toBe(1);
    });
  });

  describe('updateUser', () => {
    test('应该成功更新用户信息', async () => {
      const userData = {
        firstName: '原',
        lastName: '名',
        email: '<EMAIL>'
      };

      const user = await User.createUser(userData);
      const updatedUser = await User.updateUser(user.id, {
        firstName: '新',
        lastName: '名',
        email: '<EMAIL>'
      });

      expect(updatedUser).toBeDefined();
      expect(updatedUser.firstName).toBe('新');
      expect(updatedUser.lastName).toBe('名');
      expect(updatedUser.email).toBe('<EMAIL>');
    });

    test('应该在用户不存在时返回null', async () => {
      const result = await User.updateUser(999, { firstName: '测试' });
      expect(result).toBeNull();
    });
  });

  describe('deleteUser', () => {
    test('应该成功删除用户', async () => {
      const userData = {
        firstName: '待删除',
        lastName: '用户',
        email: '<EMAIL>'
      };

      const user = await User.createUser(userData);
      const deleted = await User.deleteUser(user.id);

      expect(deleted).toBe(true);

      // 验证用户已被删除
      const foundUser = await User.getUserById(user.id);
      expect(foundUser).toBeNull();
    });

    test('应该在用户不存在时返回false', async () => {
      const deleted = await User.deleteUser(999);
      expect(deleted).toBe(false);
    });
  });

  describe('searchUsers', () => {
    test('应该根据关键词搜索用户', async () => {
      const users = [
        { firstName: '张', lastName: '三', email: '<EMAIL>' },
        { firstName: '李', lastName: '四', email: '<EMAIL>' },
        { firstName: '张', lastName: '五', email: '<EMAIL>' }
      ];

      for (const userData of users) {
        await User.createUser(userData);
      }

      const results = await User.searchUsers('张');

      expect(results).toHaveLength(2);
      expect(results.every(user => user.firstName === '张')).toBe(true);
    });
  });
});
